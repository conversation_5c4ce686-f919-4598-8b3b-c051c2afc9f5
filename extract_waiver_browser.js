/**
 * Browser-compatible script to extract fullTextAnnotation.text from JSON files
 * This version can be used in a web browser with file input
 */

class WaiverExtractor {
    constructor() {
        this.waiverData = [];
    }

    /**
     * Extract the fullTextAnnotation.text from a single JSON object.
     * @param {Object} data - The parsed JSON data
     * @param {string} fileName - Name of the source file for logging
     * @returns {Array} Array of extracted texts from all responses
     */
    extractWaiverTextFromData(data, fileName) {
        try {
            const extractedTexts = [];

            // Navigate through ALL responses, not just responses[0]
            if (data.responses && data.responses.length > 0) {
                data.responses.forEach((response, index) => {
                    if (response.fullTextAnnotation && response.fullTextAnnotation.text) {
                        extractedTexts.push(response.fullTextAnnotation.text);
                    } else {
                        console.warn(`Warning: No fullTextAnnotation.text in ${fileName} response[${index}]`);
                    }
                });
            } else {
                console.warn(`Warning: No responses found in ${fileName}`);
            }

            return extractedTexts;

        } catch (error) {
            console.error(`Error processing ${fileName}:`, error.message);
            return [];
        }
    }

    /**
     * Process a single file
     * @param {File} file - File object from input
     * @returns {Promise<void>}
     */
    async processFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    console.log(`Processing: ${file.name}`);

                    const waiverTexts = this.extractWaiverTextFromData(data, file.name);

                    if (waiverTexts.length > 0) {
                        // Add each waiver text as a separate object
                        waiverTexts.forEach(waiverText => {
                            this.waiverData.push({
                                waiverText: waiverText,
                                sourceFile: file.name
                            });
                        });
                        console.log(`  Extracted ${waiverTexts.length} waiver texts`);
                    } else {
                        console.warn(`Warning: No text extracted from ${file.name}`);
                    }
                    
                    resolve();
                } catch (error) {
                    console.error(`Error parsing JSON from ${file.name}:`, error.message);
                    reject(error);
                }
            };
            
            reader.onerror = () => {
                reject(new Error(`Error reading file ${file.name}`));
            };
            
            reader.readAsText(file);
        });
    }

    /**
     * Process multiple files
     * @param {FileList} files - Files from input element
     * @returns {Promise<void>}
     */
    async processFiles(files) {
        this.waiverData = []; // Reset data
        
        console.log(`Found ${files.length} files`);
        
        // Convert FileList to Array and sort by name
        const fileArray = Array.from(files).sort((a, b) => a.name.localeCompare(b.name));
        
        // Process files sequentially to maintain order
        for (const file of fileArray) {
            if (file.name.endsWith('.json')) {
                await this.processFile(file);
            } else {
                console.warn(`Skipping non-JSON file: ${file.name}`);
            }
        }
        
        console.log(`\nProcessing complete!`);
        console.log(`Total waiver texts extracted: ${this.waiverData.length}`);
    }

    /**
     * Get the combined data
     * @returns {Array} Array of waiver objects
     */
    getCombinedData() {
        // Remove sourceFile property for final output
        return this.waiverData.map(item => ({
            waiverText: item.waiverText
        }));
    }

    /**
     * Download the combined data as JSON file
     * @param {string} filename - Output filename
     */
    downloadCombinedData(filename = 'combined_waiver_data_browser.json') {
        const combinedData = this.getCombinedData();
        const jsonString = JSON.stringify(combinedData, null, 2);
        
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log(`Downloaded: ${filename}`);
        console.log(`File size: ${(blob.size / 1024).toFixed(2)} KB`);
    }
}

// Usage example for browser:
/*
// HTML:
// <input type="file" id="fileInput" multiple accept=".json">
// <button onclick="processFiles()">Process Files</button>
// <button onclick="downloadData()">Download Combined Data</button>

const extractor = new WaiverExtractor();

async function processFiles() {
    const fileInput = document.getElementById('fileInput');
    if (fileInput.files.length === 0) {
        alert('Please select JSON files first');
        return;
    }
    
    try {
        await extractor.processFiles(fileInput.files);
        console.log('Files processed successfully!');
    } catch (error) {
        console.error('Error processing files:', error);
    }
}

function downloadData() {
    if (extractor.waiverData.length === 0) {
        alert('No data to download. Please process files first.');
        return;
    }
    
    extractor.downloadCombinedData();
}
*/

// For Node.js compatibility, export the class
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WaiverExtractor;
}
