#!/usr/bin/env node

/**
 * <PERSON>ript to extract fullTextAnnotation.text from JSON files and combine into a single JSON file.
 */

const fs = require('fs');
const path = require('path');

/**
 * Extract the fullTextAnnotation.text from a single JSON file.
 * @param {string} filePath - Path to the JSON file
 * @returns {Array} Array of extracted texts from all responses
 */
function extractWaiverTextFromFile(filePath) {
    try {
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const extractedTexts = [];

        // Navigate through ALL responses, not just responses[0]
        if (data.responses && data.responses.length > 0) {
            data.responses.forEach((response, index) => {
                if (response.fullTextAnnotation && response.fullTextAnnotation.text) {
                    extractedTexts.push(response.fullTextAnnotation.text);
                } else {
                    console.warn(`Warning: No fullTextAnnotation.text in ${filePath} response[${index}]`);
                }
            });
        } else {
            console.warn(`Warning: No responses found in ${filePath}`);
        }

        return extractedTexts;

    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
        return [];
    }
}

/**
 * Main function to process all JSON files and create combined output.
 */
function main() {
    const dataDir = "data";
    
    // Check if data directory exists
    if (!fs.existsSync(dataDir)) {
        console.error(`Data directory '${dataDir}' not found`);
        return;
    }
    
    // Get all JSON files in the data directory
    const files = fs.readdirSync(dataDir);
    const jsonFiles = files.filter(file => file.endsWith('.json'))
                          .map(file => path.join(dataDir, file));
    
    if (jsonFiles.length === 0) {
        console.error(`No JSON files found in ${dataDir} directory`);
        return;
    }
    
    console.log(`Found ${jsonFiles.length} JSON files`);
    
    // Sort files for consistent processing order
    jsonFiles.sort();
    
    // Extract waiver text from each file
    const waiverData = [];

    for (const filePath of jsonFiles) {
        console.log(`Processing: ${path.basename(filePath)}`);
        const waiverTexts = extractWaiverTextFromFile(filePath);

        if (waiverTexts.length > 0) {
            // Add each waiver text as a separate object
            waiverTexts.forEach(waiverText => {
                waiverData.push({
                    waiverText: waiverText
                });
            });
            console.log(`  Extracted ${waiverTexts.length} waiver texts`);
        } else {
            console.warn(`Warning: No text extracted from ${filePath}`);
        }
    }
    
    // Create output JSON
    const outputData = waiverData;
    
    // Write to output file
    const outputFile = "combined_waiver_data_js.json";
    try {
        fs.writeFileSync(outputFile, JSON.stringify(outputData, null, 2), 'utf8');
        
        console.log(`\nSuccessfully created ${outputFile}`);
        console.log(`Total waiver texts extracted: ${waiverData.length}`);
        
        // Print summary
        console.log(`\nSummary:`);
        console.log(`- Input files processed: ${jsonFiles.length}`);
        console.log(`- Waiver texts extracted: ${waiverData.length}`);
        console.log(`- Output file: ${outputFile}`);
        
        // Print file size
        const stats = fs.statSync(outputFile);
        const fileSizeInKB = (stats.size / 1024).toFixed(2);
        console.log(`- Output file size: ${fileSizeInKB} KB`);
        
    } catch (error) {
        console.error("Error writing output file:", error.message);
    }
}

// Run the script
if (require.main === module) {
    main();
}
