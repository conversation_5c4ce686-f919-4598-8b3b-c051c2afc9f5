<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Waiver Text Extractor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-section {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #fafafa;
        }
        
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
            max-width: 400px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .progress {
            margin-top: 10px;
            font-weight: bold;
        }
        
        .file-info {
            margin-top: 15px;
            font-size: 14px;
            color: #666;
        }
        
        .instructions {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .instructions ol {
            margin-bottom: 0;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ Waiver Text Extractor</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Select all JSON files from your data folder (use Ctrl/Cmd+A to select all)</li>
                <li>Click "Process Files" to extract waiver text from each file</li>
                <li>Click "Download Combined Data" to get the combined JSON file</li>
                <li>The output will be in the format: <code>[{waiverText: "..."}, {waiverText: "..."}, ...]</code></li>
            </ol>
        </div>
        
        <div class="upload-section" id="uploadSection">
            <p>📁 Select JSON files to process</p>
            <input type="file" id="fileInput" multiple accept=".json">
            <div class="file-info" id="fileInfo"></div>
        </div>
        
        <div style="text-align: center;">
            <button onclick="processFiles()" id="processBtn">🔄 Process Files</button>
            <button onclick="downloadData()" id="downloadBtn" disabled>⬇️ Download Combined Data</button>
        </div>
        
        <div class="status" id="status"></div>
    </div>

    <script src="extract_waiver_browser.js"></script>
    <script>
        const extractor = new WaiverExtractor();
        
        // File input change handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = e.target.files;
            const fileInfo = document.getElementById('fileInfo');
            
            if (files.length > 0) {
                const jsonFiles = Array.from(files).filter(f => f.name.endsWith('.json'));
                fileInfo.innerHTML = `Selected ${jsonFiles.length} JSON files (${files.length} total files)`;
                document.getElementById('processBtn').disabled = jsonFiles.length === 0;
            } else {
                fileInfo.innerHTML = '';
                document.getElementById('processBtn').disabled = true;
            }
            
            document.getElementById('downloadBtn').disabled = true;
        });
        
        // Drag and drop functionality
        const uploadSection = document.getElementById('uploadSection');
        
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            document.getElementById('fileInput').files = files;
            
            // Trigger change event
            const event = new Event('change', { bubbles: true });
            document.getElementById('fileInput').dispatchEvent(event);
        });
        
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.innerHTML = message;
            status.style.display = 'block';
        }
        
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }
        
        async function processFiles() {
            const fileInput = document.getElementById('fileInput');
            if (fileInput.files.length === 0) {
                showStatus('Please select JSON files first', 'error');
                return;
            }
            
            const processBtn = document.getElementById('processBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            
            processBtn.disabled = true;
            downloadBtn.disabled = true;
            
            try {
                showStatus('Processing files...', 'info');
                
                await extractor.processFiles(fileInput.files);
                
                const extractedCount = extractor.waiverData.length;
                showStatus(
                    `✅ Successfully processed ${extractedCount} files!<br>` +
                    `<div class="progress">Ready to download combined data</div>`, 
                    'success'
                );
                
                downloadBtn.disabled = false;
                
            } catch (error) {
                console.error('Error processing files:', error);
                showStatus(`❌ Error processing files: ${error.message}`, 'error');
            } finally {
                processBtn.disabled = false;
            }
        }
        
        function downloadData() {
            if (extractor.waiverData.length === 0) {
                showStatus('No data to download. Please process files first.', 'error');
                return;
            }
            
            try {
                extractor.downloadCombinedData();
                showStatus(
                    `✅ Download started!<br>` +
                    `<div class="progress">File: combined_waiver_data_browser.json</div>`, 
                    'success'
                );
            } catch (error) {
                console.error('Error downloading data:', error);
                showStatus(`❌ Error downloading data: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
